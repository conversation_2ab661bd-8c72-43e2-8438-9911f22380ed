package com.github.catvod.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Response;
import com.github.catvod.net.OkHttp;

/**
 * 脚本工具类，用于创建和运行脚本
 */
public class ScriptUtils {

    public static void run(File binaryFile, String[] params) {
        StringBuilder paramsStr = new StringBuilder();
        if (params != null && params.length > 0) {
            for (String param : params) {
                paramsStr.append(" ").append(param);
            }
        }
        Shell.exec("kill $(ps -ef | grep '" + binaryFile.getName() + "' | grep -v grep | awk '{print $2}')\n");
        Shell.exec("nohup " + binaryFile.getAbsolutePath() + paramsStr + " >/dev/null 2>&1 &");
    }

    /**
     * 创建并运行脚本
     *
     * @param workDir 工作目录
     * @param binaryFile 二进制文件
     * @param shellName 脚本名称
     * @param processName 进程名称（用于kill命令）
     * @param params 启动参数
     */
    public static void createAndRunScript(File workDir, File binaryFile, String shellName, String processName, String[] params) {
        File pidFile = new File(workDir, shellName + ".pid");
        File scriptFile = new File(workDir, shellName);
        String cachePath = workDir.getAbsolutePath();
        String pidPath = pidFile.getAbsolutePath();
        String binaryPath = binaryFile.getAbsolutePath();

        // 构建参数字符串
        StringBuilder paramsStr = new StringBuilder();
        if (params != null && params.length > 0) {
            for (String param : params) {
                paramsStr.append(" ").append(param);
            }
        }

        String script = "selfpid=$$\n" +
                "lastpid=$(cat " + pidPath + " 2>/dev/null || echo '')\n" +
                "echo $selfpid > " + pidPath + "\n" +
                "if [[ \"$selfpid\" != \"$lastpid\" ]]; then\n" +
                "  killall -9 " + processName + " 2>/dev/null\n" +
                "  sleep 1\n" +
                "fi\n" +
                "while true; do\n" +
                "  TMPDIR=" + cachePath + " " + binaryPath + paramsStr.toString() + " >/dev/null 2>&1\n" +
                "  lastpid=$(cat " + pidPath + " 2>/dev/null || echo '')\n" +
                "  if [[ \"$lastpid\" != \"$selfpid\" ]]; then\n" +
                "    echo \"found new pid, exit me\"\n" +
                "    exit\n" +
                "  fi\n" +
                "  sleep 5\n" +
                "done";

        Path.write(scriptFile, script);
        Shell.exec("chmod +x " + scriptFile.getAbsolutePath());

        // 杀死可能存在的旧进程
        Shell.exec("kill $(ps -ef | grep '" + processName + "' | grep -v grep | awk '{print $2}')\n");
        // 启动新脚本
        Shell.exec("nohup " + scriptFile.getAbsolutePath() + " &");
    }
}