package com.github.catvod.spider;

import android.content.Context;

import com.github.catvod.bean.Result;
import com.github.catvod.bean.Vod;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.bean.Class;
import com.github.catvod.utils.Path;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class Config extends Spider {

    @Override
    public void init(Context context, String extend) throws Exception {
        super.init(context, extend);

        File ptvConfigJsonFile = new File(Path.files(), "ptv.config.json");
        if (!ptvConfigJsonFile.exists()) {
            JSONObject jsonObject = new JSONObject();

            JSONObject danmu = new JSONObject();
            danmu.put("danmu", true);
            jsonObject.put("danmu", danmu);

            Path.write(ptvConfigJsonFile, jsonObject.toString());
        }

        String ptvConfigJson = Path.read(ptvConfigJsonFile);
        SpiderDebug.log("项目配置: " + ptvConfigJson);
    }

    @Override
    public String homeContent(boolean filter) throws Exception {
        List<Class> classes = new ArrayList<>();
        classes.add(new Class("1", "弹幕设置"));
        List<Vod> list = new ArrayList<>();
        list.add(new Vod("home", "2025-04-14 17:21", "https://raw.githubusercontent.com/Guovin/iptv-api/refs/heads/master/static/images/logo.png"));
        return Result.string(classes, list);
    }

    @Override
    public String categoryContent(String tid, String pg, boolean filter, HashMap<String, String> extend) throws JSONException {
        if (!pg.equals("1")) {
            return null;
        }
        String ptvConfigJson = Path.read(new File(Path.files(), "ptv.config.json"));
        JSONObject config = new JSONObject(ptvConfigJson);

        List<Vod> vodList = new ArrayList<>();
        //弹幕设置
        if (tid.equals("1")) {
            boolean danmu = config.optBoolean("danmu", true);
            String pic = "https://bkimg.cdn.bcebos.com/pic/ca1349540923dd54564e85faa350a4de9c82d158e252?x-bce-process=image/format,f_auto/watermark,image_d2F0ZXIvYmFpa2UyNzI,g_7,xp_5,yp_5,P_20/resize,m_lfit,limit_1,h_1080";
            String name = "关闭弹幕";
            if (!danmu) {
                name = "加载弹幕";
            }
            vodList.add(new Vod("danmu", name, pic));
        }
        return Result.get().vod(vodList).string();
    }

    @Override
    public String detailContent(List<String> ids) throws Exception {
        File ptvConfigJsonFile = new File(Path.files(), "ptv.config.json");
        String ptvConfigJson = Path.read(ptvConfigJsonFile);
        JSONObject config = new JSONObject(ptvConfigJson);

        String vodId = ids.get(0);

        if (vodId.equals("danmu")) {
            config.put("danmu", !config.optBoolean("danmu", true));
        }

        Path.write(ptvConfigJsonFile, config.toString());

        throw new RuntimeException("");
    }

    @Override
    public String playerContent(String flag, String id, List<String> vipFlags) throws Exception {
        return Result.get().url("").string();
    }
}
