package com.github.catvod.utils;

import com.github.catvod.net.OkHttp;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Response;

public class DownloadUtils {

    /**
     * 下载文件到指定路径
     *
     * @param url 下载地址
     * @param targetFile 目标文件
     * @return 是否下载成功
     */
    public static boolean downloadFile(String url, File targetFile) {
        Response response = null;
        InputStream inputStream = null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        boolean success = false;

        try {
            response = OkHttp.newCall(url);
            inputStream = response.body().byteStream();

            int len;
            byte[] buffer = new byte[1024];

            while ((len = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }

            Path.write(targetFile, baos.toByteArray());
            success = true;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.cacheResponse();
                response.close();
            }

            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                }
            }

            try {
                baos.close();
            } catch (IOException e) {
            }
        }

        return success;
    }
}