package main

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
)

// 配置结构体
type Config struct {
	DefaultChunkSize    int           // 默认分块大小（256kb）
	MaxConcurrentChunks int           // 最大并发分块数
	RequestTimeout      time.Duration // 请求超时时间
}

var cfg = Config{
	DefaultChunkSize:    256 * 1024, // 256kb
	MaxConcurrentChunks: 4,          // 默认最大并发数
	RequestTimeout:      30 * time.Second,
}

// 请求参数
type RequestParams struct {
	ChunkSize  int
	MaxThreads int
}

// 文件元数据
type FileMetadata struct {
	ContentLength int64
	AcceptRanges  string
	ContentType   string
	LastModified  time.Time
	Expires       time.Time
	ETag          string
}

// 带TTL的元数据缓存
type metadataCache struct {
	cache map[string]*FileMetadata
	mu    sync.RWMutex
}

var metadataCacheInstance = &metadataCache{
	cache: make(map[string]*FileMetadata),
}

func (c *metadataCache) get(url string) *FileMetadata {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.cache[url]
}

func (c *metadataCache) set(url string, metadata *FileMetadata) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.cache[url] = metadata
}

func main() {
	http.HandleFunc("/proxy", proxyHandler)
	log.Println("Starting server on :36150")
	log.Fatal(http.ListenAndServe(":36150", nil))
}

// 主处理函数
func proxyHandler(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), cfg.RequestTimeout)
	defer cancel()

	targetURL, err := validateAndParseURL(r.URL.Query().Get("url"))
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	params := parseRequestParams(r)
	metadata, err := getMetadata(ctx, targetURL, r.Header)
	if err != nil {
		handleError(w, "Failed to fetch metadata", err, http.StatusBadGateway)
		return
	}

	if shouldDirectDownload(r, metadata, params) {
		handleDirectRequest(ctx, w, targetURL, metadata, r.Header)
		return
	}

	handleChunkedDownload(ctx, w, r, targetURL, metadata, params)
}

// 验证并解析URL
func validateAndParseURL(rawURL string) (string, error) {
	if rawURL == "" {
		return "", errors.New("missing url parameter")
	}

	u, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("invalid URL: %w", err)
	}

	if u.Scheme != "http" && u.Scheme != "https" {
		return "", fmt.Errorf("unsupported scheme: %s", u.Scheme)
	}

	return u.String(), nil
}

// 解析请求参数
func parseRequestParams(r *http.Request) RequestParams {
	params := RequestParams{
		ChunkSize:  cfg.DefaultChunkSize,
		MaxThreads: cfg.MaxConcurrentChunks,
	}

	if cs := r.URL.Query().Get("chunkSize"); cs != "" {
		if v, err := strconv.Atoi(cs); err == nil && v > 0 {
			params.ChunkSize = v * 1024
		}
	}

	if t := r.URL.Query().Get("thread"); t != "" {
		if v, err := strconv.Atoi(t); err == nil && v > 0 {
			params.MaxThreads = v
		}
	}

	return params
}

// 获取元数据
func getMetadata(ctx context.Context, targetURL string, headers http.Header) (*FileMetadata, error) {
	// 检查缓存
	if cached := metadataCacheInstance.get(targetURL); cached != nil {
		return cached, nil
	}

	// 尝试HEAD请求
	metadata, err := fetchMetadata(ctx, targetURL, headers, "HEAD")
	if err == nil {
		return metadata, nil
	}

	// 回退到GET请求
	return fetchMetadata(ctx, targetURL, headers, "GET")
}

func fetchMetadata(ctx context.Context, targetURL string, headers http.Header, method string) (*FileMetadata, error) {
	req, err := http.NewRequestWithContext(ctx, method, targetURL, nil)
	if err != nil {
		return nil, err
	}

	copyHeaders(req.Header, headers)
	if method == "GET" {
		req.Header.Set("Range", "bytes=0-0")
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("server returned %d", resp.StatusCode)
	}

	metadata := &FileMetadata{
		ContentLength: resp.ContentLength,
		AcceptRanges:  resp.Header.Get("Accept-Ranges"),
		ContentType:   resp.Header.Get("Content-Type"),
		LastModified:  parseTimeHeader(resp.Header.Get("Last-Modified")),
		Expires:       parseTimeHeader(resp.Header.Get("Expires")),
		ETag:          resp.Header.Get("ETag"),
	}

	if method == "GET" {
		if contentRange := resp.Header.Get("Content-Range"); contentRange != "" {
			parts := strings.Split(contentRange, "/")
			if len(parts) > 1 {
				metadata.ContentLength, _ = strconv.ParseInt(parts[1], 10, 64)
			}
		}
	}

	metadataCacheInstance.set(targetURL, metadata)
	return metadata, nil
}

func parseTimeHeader(value string) time.Time {
	if value == "" {
		return time.Time{}
	}
	t, _ := time.Parse(time.RFC1123, value)
	return t
}

// 判断是否应该直连下载
func shouldDirectDownload(r *http.Request, metadata *FileMetadata, params RequestParams) bool {
	// 服务端不支持范围请求
	if metadata.AcceptRanges != "bytes" {
		return true
	}

	// 文件小于分块大小
	if metadata.ContentLength <= int64(params.ChunkSize) {
		return true
	}

	// 客户端明确要求禁用分块
	if r.URL.Query().Get("chunked") == "false" {
		return true
	}

	return false
}

// 处理直连请求
func handleDirectRequest(ctx context.Context, w http.ResponseWriter, targetURL string,
	metadata *FileMetadata, headers http.Header) {

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		handleError(w, "Failed to create request", err, http.StatusInternalServerError)
		return
	}

	copyHeaders(req.Header, headers)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		handleError(w, "Upstream request failed", err, http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	w.Header().Set("Content-Type", metadata.ContentType)
	w.Header().Set("Content-Length", strconv.FormatInt(metadata.ContentLength, 10))
	w.WriteHeader(resp.StatusCode)

	if _, err := io.Copy(w, resp.Body); err != nil {
		handleError(w, "Streaming failed", err, http.StatusInternalServerError)
	}
}

// 分块下载处理器
func handleChunkedDownload(ctx context.Context, w http.ResponseWriter, r *http.Request,
	targetURL string, metadata *FileMetadata, params RequestParams) {

	start, end := parseRangeHeader(r.Header.Get("Range"), metadata.ContentLength, params)
	contentWriter := newContentWriter(w, metadata, start, end)
	defer contentWriter.Close()

	downloader := &chunkDownloader{
		ctx:         ctx,
		targetURL:   targetURL,
		headers:     r.Header,
		chunkSize:   int64(params.ChunkSize),
		maxWorkers:  params.MaxThreads,
		totalSize:   end - start + 1,
		startOffset: start,
	}

	if err := downloader.Download(contentWriter); err != nil {
		handleError(w, "Download failed", err, http.StatusInternalServerError)
	}
}

// 内容写入器
type contentWriter struct {
	w            http.ResponseWriter
	metadata     *FileMetadata
	start        int64
	end          int64
	headerSet    bool
	bytesWritten int64
}

func newContentWriter(w http.ResponseWriter, metadata *FileMetadata, start, end int64) *contentWriter {
	return &contentWriter{
		w:        w,
		metadata: metadata,
		start:    start,
		end:      end,
	}
}

func (cw *contentWriter) Write(p []byte) (int, error) {
	if !cw.headerSet {
		cw.setHeaders()
	}
	n, err := cw.w.Write(p)
	cw.bytesWritten += int64(n)
	return n, err
}

func (cw *contentWriter) setHeaders() {
	if cw.start == 0 && cw.end == cw.metadata.ContentLength-1 {
		cw.w.Header().Set("Content-Length", strconv.FormatInt(cw.metadata.ContentLength, 10))
		cw.w.WriteHeader(http.StatusOK)
	} else {
		cw.w.Header().Set("Content-Range",
			fmt.Sprintf("bytes %d-%d/%d", cw.start, cw.end, cw.metadata.ContentLength))
		cw.w.Header().Set("Content-Length", strconv.FormatInt(cw.end-cw.start+1, 10))
		cw.w.WriteHeader(http.StatusPartialContent)
	}
	cw.w.Header().Set("Content-Type", cw.metadata.ContentType)
	cw.w.Header().Set("Accept-Ranges", "bytes")
	cw.headerSet = true
}

func (cw *contentWriter) Close() error {
	if f, ok := cw.w.(http.Flusher); ok {
		f.Flush()
	}
	return nil
}

// 分块下载器
type chunkDownloader struct {
	ctx         context.Context
	targetURL   string
	headers     http.Header
	chunkSize   int64
	maxWorkers  int
	totalSize   int64
	startOffset int64
}

func (cd *chunkDownloader) Download(w io.Writer) error {
	numChunks := int(math.Ceil(float64(cd.totalSize) / float64(cd.chunkSize)))
	taskChan := make(chan int64, numChunks)
	resultChan := make(chan downloadResult, numChunks)

	var wg sync.WaitGroup
	wg.Add(cd.maxWorkers)

	// 启动工作池
	for i := 0; i < cd.maxWorkers; i++ {
		go cd.worker(&wg, taskChan, resultChan)
	}

	// 分发任务
	go cd.dispatchTasks(taskChan, numChunks)

	// 收集结果
	return cd.collectResults(resultChan, w, numChunks)
}

func (cd *chunkDownloader) worker(wg *sync.WaitGroup, taskChan <-chan int64, resultChan chan<- downloadResult) {
	defer wg.Done()

	for offset := range taskChan {
		start := cd.startOffset + offset
		end := start + cd.chunkSize - 1
		if end > cd.startOffset+cd.totalSize-1 {
			end = cd.startOffset + cd.totalSize - 1
		}

		content, err := downloadChunk(cd.ctx, cd.targetURL, start, end, cd.headers)
		resultChan <- downloadResult{
			offset:  offset,
			content: content,
			err:     err,
		}
	}
}

func (cd *chunkDownloader) dispatchTasks(taskChan chan<- int64, numChunks int) {
	defer close(taskChan)

	for i := 0; i < numChunks; i++ {
		offset := int64(i) * cd.chunkSize
		select {
		case taskChan <- offset:
		case <-cd.ctx.Done():
			return
		}
	}
}

func (cd *chunkDownloader) collectResults(resultChan <-chan downloadResult, w io.Writer, numChunks int) error {
	buffer := make(map[int64][]byte)
	var currentOffset int64

	for i := 0; i < numChunks; i++ {
		select {
		case result := <-resultChan:
			if result.err != nil {
				return result.err
			}
			buffer[result.offset] = result.content

			// 顺序写入
			for {
				content, exists := buffer[currentOffset]
				if !exists {
					break
				}
				if _, err := w.Write(content); err != nil {
					return err
				}
				delete(buffer, currentOffset)
				currentOffset += cd.chunkSize
			}

		case <-cd.ctx.Done():
			return cd.ctx.Err()
		}
	}
	return nil
}

type downloadResult struct {
	offset  int64
	content []byte
	err     error
}

// 下载单个分块
func downloadChunk(ctx context.Context, targetURL string, start, end int64, headers http.Header) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return nil, err
	}

	copyHeaders(req.Header, headers)
	req.Header.Set("Range", fmt.Sprintf("bytes=%d-%d", start, end))

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusPartialContent {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}

// 处理错误
func handleError(w http.ResponseWriter, msg string, err error, statusCode int) {
	if isClientError(err) {
		log.Printf("Client error: %v", err)
		return
	}

	log.Printf("%s: %v", msg, err)
	http.Error(w, msg, statusCode)
}

// 判断客户端错误
func isClientError(err error) bool {
	if err == nil {
		return false
	}

	switch {
	case errors.Is(err, context.Canceled),
		strings.Contains(err.Error(), "connection reset by peer"),
		strings.Contains(err.Error(), "broken pipe"),
		strings.Contains(err.Error(), "wsasend"):
		return true
	}

	if opErr, ok := err.(*net.OpError); ok {
		return opErr.Err.Error() == "wsasend: An existing connection was forcibly closed by the remote host."
	}

	return false
}

// 解析范围请求头
func parseRangeHeader(rangeHeader string, contentLength int64, params RequestParams) (int64, int64) {
	const prefix = "bytes="
	if !strings.HasPrefix(rangeHeader, prefix) {
		return 0, contentLength - 1
	}

	rangeStr := strings.TrimPrefix(rangeHeader, prefix)
	parts := strings.SplitN(rangeStr, "-", 2)
	if len(parts) != 2 {
		return 0, contentLength - 1
	}

	start, _ := strconv.ParseInt(parts[0], 10, 64)
	endStr := parts[1]

	var end int64
	if endStr == "" {
		end = start + (int64(params.ChunkSize) * int64(params.MaxThreads)) - 1
	} else {
		end, _ = strconv.ParseInt(endStr, 10, 64)
	}

	// 边界修正
	if end >= contentLength {
		end = contentLength - 1
	}
	if start < 0 {
		start = 0
	}
	return start, end
}

// 复制请求头（过滤敏感头）
func copyHeaders(dst, src http.Header) {
	excluded := map[string]bool{
		"host":            true,
		"content-length":  true,
		"content-type":    true,
		"accept-encoding": true,
		"range":           true,
	}

	for k, vv := range src {
		if !excluded[strings.ToLower(k)] {
			dst[k] = vv
		}
	}
	dst.Set("Accept-Encoding", "identity")
}
