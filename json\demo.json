{"spider": "./jar/custom_spider.jar", "wallpaper": "http://饭太硬.top/深色壁纸/api.php", "lives": [{"name": "XtreamCode", "api": "csp_XtreamCode", "url": "http://iptv.icsnleb.com:25461/player_api.php?username=12&password=12", "epg": "http://iptv.icsnleb.com:25461/xmltv.php?username=12&password=12", "type": 3, "ext": {"live": true, "vod": true, "formats": ["m3u8", "ts"]}}], "sites": [{"key": "本地", "name": "本地", "type": 3, "api": "csp_Local", "searchable": 0, "changeable": 0}, {"key": "商店", "name": "商店", "type": 3, "api": "csp_Market", "searchable": 0, "changeable": 0, "ext": "./json/market.json"}, {"key": "push_agent", "name": "推送", "type": 3, "api": "csp_<PERSON>ush", "searchable": 0, "changeable": 0, "timeout": 60}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "proxy": ["raw.githubusercontent.com", "googlevideo.com", "googleapis.com", "youtube.com"], "rules": [{"name": "火山嗅探", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "抖音嗅探", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "農民嗅探", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "七新嗅探", "hosts": ["api.52wyb.com"], "regex": ["m3u8?pt=m3u8"]}, {"name": "毛驢點擊", "hosts": ["www.maolvys.com"], "script": ["document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"]}], "ads": ["static-mozai.4gtv.tv", "s3t3d2y8.afcdn.net"]}